defmodule Operation.Core do
  defmacro __using__(opts) do
    # IO.puts("#{__MODULE__}.__using__(#{inspect(opts)})")

    quote do
      import Operation.Core

      if unquote(opts[:source_module]) do
        @opts Operation.Core.merge_opts(
                unquote(opts[:source_module]).__opts__(),
                unquote(opts)
              )
      else
        @opts unquote(opts)
      end

      def __opts__, do: @opts

      @extensions @opts[:extensions]
      def __extensions__, do: @extensions

      @unit_of_work Drops.Operations.UnitOfWork.new(__MODULE__, [])

      @before_compile Operation.Core
    end
  end

  defmacro __before_compile__(_env) do
    # IO.inspect(env.module, label: "#{__MODULE__}.__before_compile__")
  end

  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  def merge_opts(parent_opts, new_opts) do
    extensions =
      Keyword.get(new_opts, :extensions, []) ++ Keyword.get(parent_opts, :extensions, [])

    Keyword.merge(parent_opts, new_opts) |> Keyword.put(:extensions, extensions)
  end
end

defmodule Operation.Extension do
  @callback enabled?(opts :: keyword()) :: boolean()

  @callback unit_of_work(uow :: map(), opts :: keyword()) :: map()

  @callback extend(extension :: module(), operation :: module(), opts :: keyword()) ::
              Macro.t()

  @optional_callbacks extend: 3

  defmacro __using__(opts) do
    quote do
      @behaviour Operation.Extension

      import Operation.Core, only: [steps: 1]

      @opts unquote(opts)
      def __opts__, do: @opts

      def enabled?(_opts), do: true
      defoverridable enabled?: 1

      def unit_of_work(uow, _opts), do: uow
      defoverridable unit_of_work: 2

      def helpers, do: []
      defoverridable helpers: 0

      defdelegate extend(extension, operation, opts), to: Operation.Extension

      defmacro __using__(opts) do
        __MODULE__.extend(__MODULE__, __CALLER__.module, opts)
      end
    end
  end

  def extend(extension, operation, opts) do
    # IO.puts("#{__MODULE__}.extend(#{inspect(operation)}, #{inspect(opts)}) with #{extension}")

    unit_of_work =
      extension.unit_of_work(Module.get_attribute(operation, :unit_of_work), opts)

    Module.put_attribute(operation, :unit_of_work, unit_of_work)
  end
end

defmodule Operation.Extensions.Command do
  use Operation.Extension

  def unit_of_work(uow, _opts) do
    uow
    |> Drops.Operations.UnitOfWork.add_step(:prepare)
    |> Drops.Operations.UnitOfWork.add_step(:validate)
    |> Drops.Operations.UnitOfWork.add_step(:execute)
  end

  steps do
    def prepare(context) do
      {:ok, Map.put(context, :prepared, true)}
    end

    def validate(context) do
      {:ok, context}
    end

    def execute(_context) do
      raise "#{__MODULE__}.execute/1 must be implemented"
    end

    defoverridable prepare: 1, validate: 1, execute: 1
  end
end

defmodule Operation.Extensions.Ecto do
  use Operation.Extension

  def unit_of_work(uow, _opts) do
    uow
    |> Drops.Operations.UnitOfWork.after_step(:prepare, :changeset)
  end

  steps do
    def changeset(context) do
      {:ok, Map.put(context, :changeset, get_struct(context))}
    end

    def validate(context) do
      {:ok, Map.put(context, :validated, true)}
    end

    def get_struct(_context) do
      %{}
    end

    defoverridable changeset: 1, validate: 1, get_struct: 1
  end
end

defmodule Operation do
  use Operation.Core, type: :abstract, extensions: [Operation.Extensions.Ecto]

  defmacro __using__(opts) do
    # IO.puts("#{__MODULE__}.__using__(#{inspect(opts)})")

    extend(__MODULE__, __CALLER__.module, opts)
  end

  defmacro __before_compile__(env) do
    # IO.puts("#{__MODULE__}.__before_compile__(#{inspect(env.module)})")

    module = env.module

    enabled_extensions =
      Enum.filter(Module.get_attribute(module, :extensions), fn extension ->
        extension.enabled?(Module.get_attribute(module, :opts))
      end)

    Module.put_attribute(module, :enabled_extensions, enabled_extensions)

    extension_code =
      Enum.map(enabled_extensions, fn extension ->
        quote do
          use unquote(extension)
        end
      end)

    extension_steps = Enum.map(enabled_extensions, fn extension -> extension.steps() end)
    custom_steps = Module.get_attribute(module, :steps, [])

    quote do
      unquote_splicing(extension_code)

      unquote_splicing(extension_steps)

      unquote(custom_steps)

      def __unit_of_work__, do: @unit_of_work
    end
  end

  def extend(source_module, _target_module, opts) do
    # IO.puts("#{__MODULE__}.extend(#{inspect(source_module)}, #{inspect(target_module)}, #{inspect(opts)})")

    quote do
      use Operation.Core, unquote(Keyword.put(opts, :source_module, source_module))

      @before_compile Operation

      import Operation

      defmacro __using__(opts) do
        # IO.puts("#{__MODULE__}.__using__(#{inspect(opts)})")

        extend(__MODULE__, __CALLER__.module, opts)
      end

      def call(context) do
        Drops.Operations.UnitOfWork.process(__unit_of_work__(), context)
      end
    end
  end
end

defmodule Operation.Command do
  use Operation, type: :command, extensions: [Operation.Extensions.Command]
end

defmodule MyApp.SaveUser do
  use Operation.Command

  steps do
    def execute(context) do
      {:ok, Map.put(context, :executed, true)}
    end

    def get_struct(context) do
      %{name: context.params.name}
    end
  end
end

IO.inspect(Operation.__opts__(), label: "Operation.__opts__")
IO.inspect(Operation.Command.__opts__(), label: "Operation.Command.__opts__")

IO.inspect(MyApp.SaveUser.__opts__(), label: "MyApp.SaveUser.__opts__")
IO.inspect(MyApp.SaveUser.__unit_of_work__(), label: "MyApp.SaveUser.__unit_of_work__")

result = MyApp.SaveUser.call(%{params: %{name: "Jane"}})
IO.inspect(result, label: "result")
