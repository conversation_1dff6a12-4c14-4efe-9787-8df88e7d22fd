defmodule Drops.Operations.Core do
  defmacro __using__(opts) do
    setup_opts =
      quote do
        @opts unquote(merge_opts(opts[:source_module], opts))
        def __opts__, do: @opts
      end

    setup_extensions =
      quote do
        Module.put_attribute(__MODULE__, :extensions, @opts[:extensions])

        @extensions Module.get_attribute(__MODULE__, :extensions)
        def __extensions__, do: @extensions

        Module.put_attribute(
          __MODULE__,
          :enabled_extensions,
          Enum.filter(@extensions, & &1.enabled?(@opts))
        )

        @enabled_extensions Module.get_attribute(__MODULE__, :enabled_extensions)
        def __enabled_extensions__, do: @enabled_extensions
      end

    quote location: :keep do
      import Drops.Operations.Core

      unquote(setup_opts)
      unquote(setup_extensions)

      @unit_of_work Drops.Operations.UnitOfWork.new(__MODULE__, [])
    end
  end

  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  def merge_opts(nil, new_opts), do: new_opts

  def merge_opts(module, new_opts) when is_atom(module) and is_list(new_opts) do
    merge_opts(module.__opts__(), new_opts)
  end

  def merge_opts(parent_opts, new_opts) when is_list(parent_opts) and is_list(new_opts) do
    extensions =
      Keyword.get(new_opts, :extensions, []) ++ Keyword.get(parent_opts, :extensions, [])

    Keyword.merge(parent_opts, new_opts) |> Keyword.put(:extensions, extensions)
  end
end
