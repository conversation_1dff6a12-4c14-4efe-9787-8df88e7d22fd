defmodule Drops.Operations.Extensions.Ecto do
  use Drops.Operations.Extension

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :repo) && !is_nil(opts[:repo])
  end

  @impl true
  def unit_of_work(uow, _opts) do
    uow
    |> Drops.Operations.UnitOfWork.after_step(:prepare, :changeset)
  end

  @impl true
  def extend(_extension, _operation, opts) do
    quote location: :keep do
      import Ecto.Changeset

      def ecto_schema, do: schema().meta[:source_schema]

      def repo, do: unquote(opts[:repo])

      def insert(changeset) do
        repo().insert(%{changeset | action: :insert})
      end

      def update(changeset) do
        repo().update(%{changeset | action: :update})
      end

      def changeset(%{params: params} = context) do
        struct = get_struct(context)
        schema_module = ecto_schema()
        embedded_fields = schema_module.__schema__(:embeds)

        changeset = change(struct, params)
        changeset = cast_embedded_fields(changeset, embedded_fields, params)

        {:ok, Map.put(context, :changeset, changeset)}
      end

      def validate(%{changeset: changeset} = context) do
        case validate_changeset(%{context | changeset: %{changeset | action: :validate}}) do
          %{valid?: true} = changeset ->
            {:ok, %{context | changeset: %{changeset | action: nil}}}

          changeset ->
            {:error, changeset}
        end
      end

      def validate_changeset(%{changeset: changeset}) do
        changeset
      end

      def get_struct(_context) do
        struct(ecto_schema())
      end

      defp cast_embedded_fields(changeset, embedded_fields, params) do
        Enum.reduce(embedded_fields, changeset, fn field, acc ->
          if Map.has_key?(params, field) do
            cast_embed(acc, field)
          else
            acc
          end
        end)
      end

      defoverridable changeset: 1, validate: 1, validate_changeset: 1, get_struct: 1
    end
  end

  defmacro __before_compile__(_env) do
    quote do
    end
  end
end
