defmodule Drops.Operations.Extensions.Command do
  use Drops.Operations.Extension

  @impl true
  def extend(_, _, _) do
    quote do
    end
  end

  @impl true
  def unit_of_work(uow, _opts) do
    uow
    |> Drops.Operations.UnitOfWork.add_step(:prepare)
    |> Drops.Operations.UnitOfWork.add_step(:validate)
    |> Drops.Operations.UnitOfWork.add_step(:execute)
  end

  steps do
    def prepare(context) do
      {:ok, context}
    end

    def validate(context) do
      {:ok, context}
    end

    def execute(_context) do
      raise "#{__MODULE__}.execute/1 must be implemented"
    end

    defoverridable prepare: 1, validate: 1, execute: 1
  end
end
