defmodule Drops.Operations.Extensions.Params do
  use Drops.Operations.Extension

  @impl true
  def unit_of_work(uow, _opts) do
    schemas = Module.get_attribute(uow.module, :schemas)
    schema = schemas[:default]

    if not is_nil(schema) and schema.keys != [] do
      uow |> Drops.Operations.UnitOfWork.before_step(:prepare, :conform)
    else
      uow
    end
  end

  @impl true
  def extend(source_module, target_module, opts) do
    IO.puts(
      "#{__MODULE__}.extend(#{inspect(source_module)}, #{inspect(target_module)}, #{inspect(opts)})"
    )

    quote do
      use Drops.Contract

      schema do
        %{}
      end

      def conform(%{params: params} = context) do
        case super(params) do
          {:ok, conformed_params} ->
            {:ok, Map.put(context, :params, conformed_params)}

          {:error, _} = error ->
            error
        end
      end
    end
  end
end
