defmodule Drops.Operations do
  use Drops.Operations.Core,
    type: :abstract,
    extensions: [
      Drops.Operations.Extensions.Command,
      Drops.Operations.Extensions.Params,
      Drops.Operations.Extensions.Ecto
    ]

  alias Drops.Operations.UnitOfWork

  defmacro __using__(opts) do
    define(__MODULE__, opts)
  end

  def define(source_module, opts) do
    quote do
      import Drops.Operations

      use Drops.Operations.Core, unquote(Keyword.put(opts, :source_module, source_module))
      use Drops.Operations.Extensions

      @before_compile Drops.Operations

      defmacro __using__(opts) do
        define(__MODULE__, opts)
      end

      def call(context) do
        UnitOfWork.process(__unit_of_work__(), context)
      end

      def call({:ok, previous_result}, context) do
        UnitOfWork.process(
          __unit_of_work__(),
          Map.put(context, :execute_result, previous_result)
        )
      end

      def call({:error, _error} = error_result, _input) do
        error_result
      end
    end
  end

  defmacro __before_compile__(env) do
    module = env.module

    enabled_extensions = Module.get_attribute(module, :enabled_extensions)
    extension_steps = Enum.map(enabled_extensions, fn extension -> extension.steps() end)
    custom_steps = Module.get_attribute(module, :steps, [])

    quote do
      unquote_splicing(extension_steps)

      unquote(custom_steps)

      def __unit_of_work__, do: @unit_of_work
    end
  end
end
